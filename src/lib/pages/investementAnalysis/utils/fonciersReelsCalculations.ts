import { FormDataInvestementAnalysistSteps, IInvestementCalculatorData } from '~/lib/types/investement-analysis';
import {
  FonciersReelsFormData,
  ProjectionDataItem,
  PlusValueDataItem,
  SCIPartsDataItem,
} from '~/lib/types/fonciers-reels';

/**
 * Calculates all the data for the Fonciers Réels tax regime
 * @param data Input data from the parent component
 * @returns Calculated data for the Fonciers Réels form
 */
export function calculateFonciersReelsData(
  data: FormDataInvestementAnalysistSteps
): Partial<FonciersReelsFormData> {
  // Get values from previous steps
  const loyersAnnuels =
    data?.investementCalculatorData?.loyersAnnuelHorsCharges || 0;
  const montantInvestissement =
    data?.realEstateData?.prixAcquisitionMontant || 0;
  const remboursementEmprunt =
    data?.investementCalculatorData?.monthlyPayment * 12 || 0;
  const tauxMarginalImposition =
    data?.investementCalculatorData?.tauxMarginalImposition || 0.3;
  const nombreParts = data?.investementCalculatorData?.nombreParts || 1;

  // Get profile investor data
  const assetsInfoTable = data?.profileInvestorData?.assetsInfoTable || [];

  // Count properties with different tax regimes
  let microFonciers = data.profileInvestorData?.taxRegimeTotals?.micro_fonciers || 0;
  let foncierReels = data.profileInvestorData?.taxRegimeTotals?.fonciers_reel || 0;

  // Fonciers Réels conditions
  const locationsNues = data.realEstateData.locationsNues; // Assuming always yes for this form
  const autresRevenusLocatifsNue = assetsInfoTable.length > 0 ? 'Oui' : 'Non';

  // Check if Fonciers Réels regime is possible
  // Based on the Excel formula: =IF(H5>0,"Non","Oui")
  const regimeFonciersReelPossible = microFonciers === 0;

  // Determine if Fonciers Réels regime is applicable
  // Convert boolean result to "Oui" or "Non" string
  const concatener = locationsNues === 'Oui' && regimeFonciersReelPossible ? 'Oui' : 'Non';

  // Check if Fonciers Réels regime is applicable
  const regimeFonciersReelApplicable = concatener === 'Oui';

  // Check if CFE is applicable
  // Based on the Excel formula: =IF($H$3>100000,"Oui","Non")
  const cfeApplicable = loyersAnnuels > 100000;

  // Get application indice
  // Based on formula: =IF('Infos à renseigner'!$B$4="Oui",2%,0)
  const applicationIndice = data?.realEstateData?.applicationIndice || 'Non';
  const indiceMoyen = applicationIndice === 'Oui' ? 0.02 : 0; // 2% if application indice is enabled
  const currentYear = new Date(
    data.investementCalculatorData.dateAcquisition
  ).getFullYear();

  // Get charges data from investementCalculatorData
  const taxeFonciere = data?.investementCalculatorData?.taxesFoncieres || 0;
  const chargesLocatives = data?.investementCalculatorData?.chargesLocatives || 0;
  const interetsEmprunts = data?.investementCalculatorData?.interetsEmprunt || 0;
  const fraisEmprunts = data?.investementCalculatorData?.fraisEmprunts || 0;
  const fraisAgences = data?.investementCalculatorData?.fraisAgences || 0;
  const primesAssurances = data?.investementCalculatorData?.assurancesNonOccupant + data?.investementCalculatorData?.assurancesEmprunteur || 0;

  // Get travaux data from realEstateData (only deductible works)
  const travauxFromRealEstate = data?.realEstateData?.travauxTotal || 0;
  const travauxFromInvestment = data?.investementCalculatorData?.travauxDeductibles || 0;
  const travaux = travauxFromRealEstate + travauxFromInvestment;

  // Calculate projection data
  const projectionData = calculateProjectionData(
    loyersAnnuels,
    indiceMoyen,
    taxeFonciere,
    chargesLocatives,
    interetsEmprunts,
    fraisEmprunts,
    fraisAgences,
    primesAssurances,
    travaux,
    tauxMarginalImposition,
    remboursementEmprunt,
    currentYear,
    data?.investementCalculatorData?.loanCalculatedData,
    data?.investementCalculatorData?.dateAcquisition
  );

  // Check if the limit is exceeded in any year
  const limitExceededYear = projectionData.find(
    (item) => item.limitExceeded
  )?.limitExceededYear;

  // Calculate plus-value data, stopping at the year when the limit is exceeded if applicable
  const plusValueData = calculatePlusValueData(
    montantInvestissement,
    nombreParts,
    data.investementCalculatorData,
    limitExceededYear
  );

  // Calculate SCI parts data with projection data
  const sciPartsData = calculateSCIPartsData(
    montantInvestissement,
    nombreParts,
    projectionData
  );

  return {
    // Conditions
    locationsNues,
    loyersAnnuels,
    autresRevenusLocatifsNue,
    microFonciers,
    foncierReels,
    regimeFonciersReelPossible,
    concatener,
    regimeFonciersReelApplicable,
    cfeApplicable,

    // Projections
    indiceMoyen,
    projectionData,
    limitExceededYear,
    plusValueData,
    sciPartsData,
  };
}

/**
 * Helper function to calculate annual interest from loan schedule
 * Mimics SUMIF functionality from Excel to sum interest by year
 */
function calculateAnnualInterestFromLoanSchedule(
  loanSchedule: any[],
  year: number,
  startDate: string
): number {
  if (!loanSchedule || loanSchedule.length === 0) {
    console.log(`Year ${year}: No loan schedule data`);
    return 0;
  }

  const targetYear = new Date(startDate).getFullYear() + year + 2;
  let annualInterest = 0;
  let entriesFound = 0;

  for (const entry of loanSchedule) {
    // Parse French date format (dd/MM/yyyy)
    const dateParts = entry.DATE_ECHEANCE.split('/');
    if (dateParts.length === 3) {
      const entryDate = new Date(parseInt(dateParts[2]), parseInt(dateParts[1]) - 1, parseInt(dateParts[0]));
      if (entryDate.getFullYear() === targetYear) {
        entriesFound++;
        // Parse the interest amount (remove € and convert to number)
        const interestAmount = parseFloat(entry.INTERETS.replace(/[€\s]/g, '').replace(',', '.'));
        if (!isNaN(interestAmount)) {
          annualInterest += interestAmount;
        }
      }
    }
  }

  const result = Math.round(annualInterest);
  return result;
}

/**
 * Calculates projection data for 30 years
 * Based on the Excel formulas for Fonciers Réels
 */
function calculateProjectionData(
  loyersAnnuels: number,
  indiceMoyen: number,
  taxeFonciere: number,
  chargesLocatives: number,
  interetsEmprunts: number,
  fraisEmprunts: number,
  fraisAgences: number,
  primesAssurances: number,
  travaux: number,
  tauxMarginalImposition: number,
  remboursementEmprunt: number,
  currentYear: number,
  loanSchedule?: any[],
  startDate?: string
): ProjectionDataItem[] {
  const projectionData: ProjectionDataItem[] = [];
  let cumulTresorerie = 0;
  const plafondDeficitImputable = 10700; // Plafond déficit imputable (10 700€)
  const revenueLimit = 100000; // Revenue limit for CFE applicability

  for (let i = 0; i < 30; i++) {
    // Year calculation (using 1-based index for year number)
    const annee = currentYear + i + 1;

    // Calculate annual revenue with index increase
    // Based on formula: =IF($B$14>0,C15*(1+$B$14),$C$15)
    let revenusLocatifsHorsCharges: number;
    if (i === 0) {
      revenusLocatifsHorsCharges = loyersAnnuels;
    } else {
      revenusLocatifsHorsCharges =
        indiceMoyen > 0
          ? Math.round(
              projectionData[i - 1].revenusLocatifsHorsCharges *
                (1 + indiceMoyen)
            )
          : loyersAnnuels;
    }

    // Check if revenue exceeds the limit (100,000€ for CFE applicability)
    if (revenusLocatifsHorsCharges > revenueLimit) {
      // Calculate all values for the limit year and mark it as exceeded
      // Calculate tax fonciere with 5% annual increase
      let taxeFonciereIndexee: number;
      if (i === 0) {
        taxeFonciereIndexee = taxeFonciere;
      } else {
        taxeFonciereIndexee = Math.round(
          projectionData[i - 1].taxeFonciere * 1.05
        );
      }

      // Charges locatives (1/3 of total charges)
      const chargesLocativesIndexees = chargesLocatives * (1 / 3);

      // Interest on loans from loan table using SUMIF-like calculation
      // For the first year (i=0), use the original interetsEmprunts value
      // For subsequent years, use the loan schedule data
      const interetsEmpruntsAnnee = i === 0
        ? interetsEmprunts
        : (loanSchedule && startDate
          ? calculateAnnualInterestFromLoanSchedule(loanSchedule, i - 1, startDate)
          : (i < 25 ? Math.max(0, interetsEmprunts * (1 - i / 25)) : 0));

      // Frais d'emprunts (only in first year)
      const fraisEmpruntsIndexes = i === 0 ? fraisEmprunts : 0;

      // Frais d'agences (constant or default to 20)
      const fraisAgencesIndexes = fraisAgences > 0 ? fraisAgences : 20;

      // Primes d'assurances (constant)
      const primesAssurancesIndexees = primesAssurances;

      // Travaux (only in first year)
      const travauxIndexes = i === 0 ? travaux : 0;

      // Calculate property income result
      const resultatFonciers =
        revenusLocatifsHorsCharges -
        taxeFonciereIndexee -
        chargesLocativesIndexees -
        travauxIndexes -
        interetsEmpruntsAnnee -
        fraisEmpruntsIndexes -
        fraisAgencesIndexes -
        primesAssurancesIndexees;

      // Calculate cumulative result based on Excel formula: =IF(C31<0,C31+D26,D26)
      // First year starts at 0, then applies cumulative logic from year 2 onwards
      let resultatFonciersCumulatifs: number;
      if (i === 0) {
        resultatFonciersCumulatifs = 0; // First year shows 0
      } else {
        const previousCumulative = projectionData[i - 1].resultatFonciersCumulatifs;
        if (resultatFonciers < 0) {
          // If current result is negative, add it to previous cumulative
          resultatFonciersCumulatifs = resultatFonciers + previousCumulative;
        } else {
          // If current result is positive or zero, just use previous cumulative
          resultatFonciersCumulatifs = previousCumulative;
        }
      }

      // Calculate deficit that can be deducted from income
      // French tax law: deficit can only be deducted from global income for the first 3 years
      // Excel formula for year 1: =IF(C26<-10700,10700,IF(C26<0,ABS(C26),0))
      // Excel formula for year 2+: =IF(D27<-10700,10700,IF(D27<0,ABS(D27),0)) where D27 is cumulative result
      let deficitImputable: number;
      if (i < 3) {
        // First 3 years: can use deficit deduction
        // For year 1, use resultatFonciers directly
        // For years 2-3, use cumulative result to determine deficit deduction
        const resultToCheck = i === 0 ? resultatFonciers : resultatFonciersCumulatifs;

        if (resultToCheck < -plafondDeficitImputable) {
          deficitImputable = plafondDeficitImputable;
        } else if (resultToCheck < 0) {
          deficitImputable = Math.abs(resultToCheck);
        } else {
          deficitImputable = 0;
        }
      } else {
        // Years 4 and beyond: no deficit deduction allowed
        deficitImputable = 0;
      }

      // Calculate deficit that can be carried forward (reportable result after global income imputation)
      // This represents the cumulative result that can be carried forward to future years
      // Pattern: Year 1-3 = resultatFonciers + deficitImputable (when deficit deduction is allowed)
      //          Year 4+ = previous_reportable + current_result (no deficit deduction)
      let resultatFonciersReportables: number;
      if (i === 0) {
        // First year: resultatFonciers + deficitImputable = -52,825 + 10,700 = -42,125
        resultatFonciersReportables = resultatFonciers + deficitImputable;
      } else {
        const previousReportable = projectionData[i - 1].resultatFonciersReportables;
        if (previousReportable < 0) {
          // If there's a previous deficit to carry forward
          if (i < 3) {
            // Years 2-3: can still use deficit deduction
            resultatFonciersReportables = previousReportable + resultatFonciers + deficitImputable;
          } else {
            // Years 4+: no deficit deduction allowed
            resultatFonciersReportables = previousReportable + resultatFonciers;
          }
        } else {
          // If no previous deficit, just use current result
          resultatFonciersReportables = resultatFonciers;
        }
      }

      // Calculate income tax
      const impotRevenu =
        resultatFonciers <= 0 ? 0 : resultatFonciers * tauxMarginalImposition;

      // Calculate social charges (17.2% of positive income)
      const prelevementsSociaux =
        resultatFonciers <= 0 ? 0 : resultatFonciers * 0.172;

      // Calculate deductible CSG (6.8% of positive income)
      const csgDeductible = resultatFonciers <= 0 ? 0 : -resultatFonciers * 0.068;

      // Calculate total income tax
      const irpp = impotRevenu + prelevementsSociaux + csgDeductible;

      // Calculate cash flow before loan payments
      const tresorerieDegageeHorsEmprunts = resultatFonciers - irpp;

      // Calculate loan payments based on loan duration
      const remboursementsEmprunts = i < 25 ? remboursementEmprunt : 0;

      // Calculate total cash flow
      const tresorerieTotaleDegagee =
        tresorerieDegageeHorsEmprunts - remboursementsEmprunts;

      // Calculate cash flow with tax savings
      const tresorerieTotaleDegageeAvecEcoIR =
        deficitImputable > 0
          ? tresorerieTotaleDegagee + deficitImputable * tauxMarginalImposition
          : tresorerieTotaleDegagee;

      // Update cumulative cash flow
      if (i === 0) {
        cumulTresorerie = tresorerieTotaleDegagee;
      } else {
        cumulTresorerie =
          projectionData[i - 1].tresorerieTotalCumul + tresorerieTotaleDegagee;
      }

      // Add the year when the limit is exceeded with a flag
      projectionData.push({
        annee,
        revenusLocatifsHorsCharges,
        travaux: travauxIndexes,
        taxeFonciere: taxeFonciereIndexee,
        chargesLocatives: chargesLocativesIndexees,
        interetsEmprunts: interetsEmpruntsAnnee,
        fraisEmprunts: fraisEmpruntsIndexes,
        fraisAgences: fraisAgencesIndexes,
        primesAssurances: primesAssurancesIndexees,
        resultatFonciers,
        resultatFonciersCumulatifs,
        plafondDeficitImputable: deficitImputable,
        resultatFonciersReportables,
        tauxMarginalImposition,
        prelevementsSociaux,
        csgDeductible,
        irpp,
        tresorerieDegageeHorsEmprunts,
        remboursementsEmprunts,
        tresorerieTotaleDegagee,
        tresorerieTotaleDegageeAvecEcoIR,
        tresorerieTotalCumul: cumulTresorerie,
        limitExceeded: true,
        limitExceededYear: annee,
      });

      // Stop the loop after adding the year when the limit is exceeded
      break;
    }

    // Calculate tax fonciere with 5% annual increase
    // Based on formula: =+C18*1.05
    let taxeFonciereIndexee: number;
    if (i === 0) {
      taxeFonciereIndexee = taxeFonciere;
    } else {
      taxeFonciereIndexee = Math.round(
        projectionData[i - 1].taxeFonciere * 1.05
      );
    }

    // Charges locatives (1/3 of total charges)
    // Based on formula: =+'Cas d''étude - 1ère année'!$B$16*(0.333333333333333)
    const chargesLocativesIndexees = chargesLocatives * (1 / 3);

    // Interest on loans from loan table using SUMIF-like calculation
    // For the first year (i=0), use the original interetsEmprunts value
    // For subsequent years, use the loan schedule data
    const interetsEmpruntsAnnee = i === 0
      ? interetsEmprunts
      : (loanSchedule && startDate
        ? calculateAnnualInterestFromLoanSchedule(loanSchedule, i - 1, startDate)
        : (i < 25 ? Math.max(0, interetsEmprunts * (1 - i / 25)) : 0));

    // Frais d'emprunts (only in first year)
    const fraisEmpruntsIndexes = i === 0 ? fraisEmprunts : 0;

    // Frais d'agences (constant or default to 20)
    // Based on formula: =IF('Cas d''étude - 1ère année'!$B$21>0,'Cas d''étude - 1ère année'!$B$21,20)
    const fraisAgencesIndexes = fraisAgences > 0 ? fraisAgences : 20;

    // Primes d'assurances (constant)
    const primesAssurancesIndexees = primesAssurances;

    // Travaux (only in first year)
    // Based on formula: =IF('Infos à renseigner'!$B$27="Oui",'Infos à renseigner'!$D$19,0)
    const travauxIndexes = i === 0 ? travaux : 0;

    // Calculate property income result
    // Based on formula: =+C15-SUM(C17:C23)
    const resultatFonciers =
      revenusLocatifsHorsCharges -
      taxeFonciereIndexee -
      chargesLocativesIndexees -
      travauxIndexes -
      interetsEmpruntsAnnee -
      fraisEmpruntsIndexes -
      fraisAgencesIndexes -
      primesAssurancesIndexees;

    // Calculate cumulative result based on Excel formula: =IF(C31<0,C31+D26,D26)
    // First year starts at 0, then applies cumulative logic from year 2 onwards
    let resultatFonciersCumulatifs: number;
    if (i === 0) {
      resultatFonciersCumulatifs = 0; // First year shows 0
    } else {
      const previousCumulative = projectionData[i - 1].resultatFonciersCumulatifs;
      if (resultatFonciers < 0) {
        // If current result is negative, add it to previous cumulative
        resultatFonciersCumulatifs = resultatFonciers + previousCumulative;
      } else {
        // If current result is positive or zero, just use previous cumulative
        resultatFonciersCumulatifs = previousCumulative;
      }
    }

    // Calculate deficit that can be deducted from income
    // French tax law: deficit can only be deducted from global income for the first 3 years
    // Excel formula for year 1: =IF(C26<-10700,10700,IF(C26<0,ABS(C26),0))
    // Excel formula for year 2+: =IF(D27<-10700,10700,IF(D27<0,ABS(D27),0)) where D27 is cumulative result
    let deficitImputable: number;
    if (i < 3) {
      // First 3 years: can use deficit deduction
      // For year 1, use resultatFonciers directly
      // For years 2-3, use cumulative result to determine deficit deduction
      const resultToCheck = i === 0 ? resultatFonciers : resultatFonciersCumulatifs;

      if (resultToCheck < -plafondDeficitImputable) {
        deficitImputable = plafondDeficitImputable;
      } else if (resultToCheck < 0) {
        deficitImputable = Math.abs(resultToCheck);
      } else {
        deficitImputable = 0;
      }
    } else {
      // Years 4 and beyond: no deficit deduction allowed
      deficitImputable = 0;
    }

    // Calculate deficit that can be carried forward (reportable result after global income imputation)
    // This represents the cumulative result that can be carried forward to future years
    // Pattern: Year 1-3 = resultatFonciers + deficitImputable (when deficit deduction is allowed)
    //          Year 4+ = previous_reportable + current_result (no deficit deduction)
    let resultatFonciersReportables: number;
    if (i === 0) {
      // First year: resultatFonciers + deficitImputable = -52,825 + 10,700 = -42,125
      resultatFonciersReportables = resultatFonciers + deficitImputable;
    } else {
      const previousReportable = projectionData[i - 1].resultatFonciersReportables;
      if (previousReportable < 0) {
        // If there's a previous deficit to carry forward
        if (i < 3) {
          // Years 2-3: can still use deficit deduction
          resultatFonciersReportables = previousReportable + resultatFonciers + deficitImputable;
        } else {
          // Years 4+: no deficit deduction allowed
          resultatFonciersReportables = previousReportable + resultatFonciers;
        }
      } else {
        // If no previous deficit, just use current result
        resultatFonciersReportables = resultatFonciers;
      }
    }

    // Calculate income tax
    // Based on formula: =IF(C31<0,0,IF(C31>0,C31*$B$34))
    const impotRevenu =
      resultatFonciers <= 0 ? 0 : resultatFonciers * tauxMarginalImposition;

    // Calculate social charges (17.2% of positive income)
    // Based on formula: =IF(C31<0,0,IF(C31>0,C31*$B$35))
    const prelevementsSociaux =
      resultatFonciers <= 0 ? 0 : resultatFonciers * 0.172;

    // Calculate deductible CSG (6.8% of positive income)
    // Based on formula: =IF(C31<0,0,IF(C31>0,-C31*$B$36))
    const csgDeductible = resultatFonciers <= 0 ? 0 : -resultatFonciers * 0.068;

    // Calculate total income tax
    // Based on formula: =SUM(C34:C36)
    const irpp = impotRevenu + prelevementsSociaux + csgDeductible;

    // Calculate cash flow before loan payments
    // Based on formula: =C26-C37
    const tresorerieDegageeHorsEmprunts = resultatFonciers - irpp;

    // Calculate loan payments based on loan duration
    // We'll use a simplified model assuming 25-year loan
    const remboursementsEmprunts = i < 25 ? remboursementEmprunt : 0;

    // Calculate total cash flow
    // Based on formula: =C39-C42
    const tresorerieTotaleDegagee =
      tresorerieDegageeHorsEmprunts - remboursementsEmprunts;

    // Calculate cash flow with tax savings
    // Based on formula: =IF(C29>0,C43+C29*'Cas d''étude - 1ère année'!$B$8)
    const tresorerieTotaleDegageeAvecEcoIR =
      deficitImputable > 0
        ? tresorerieTotaleDegagee + deficitImputable * tauxMarginalImposition
        : tresorerieTotaleDegagee;

    // Update cumulative cash flow
    // Based on formula: =+C43 for first year, =+C45+D43 for subsequent years
    if (i === 0) {
      cumulTresorerie = tresorerieTotaleDegagee;
    } else {
      cumulTresorerie =
        projectionData[i - 1].tresorerieTotalCumul + tresorerieTotaleDegagee;
    }

    projectionData.push({
      annee,
      revenusLocatifsHorsCharges,
      travaux: travauxIndexes,
      taxeFonciere: taxeFonciereIndexee,
      chargesLocatives: chargesLocativesIndexees,
      interetsEmprunts: interetsEmpruntsAnnee,
      fraisEmprunts: fraisEmpruntsIndexes,
      fraisAgences: fraisAgencesIndexes,
      primesAssurances: primesAssurancesIndexees,
      resultatFonciers,
      resultatFonciersCumulatifs,
      plafondDeficitImputable: deficitImputable,
      resultatFonciersReportables,
      tauxMarginalImposition,
      prelevementsSociaux,
      csgDeductible,
      irpp,
      tresorerieDegageeHorsEmprunts,
      remboursementsEmprunts,
      tresorerieTotaleDegagee,
      tresorerieTotaleDegageeAvecEcoIR,
      tresorerieTotalCumul: cumulTresorerie,
      limitExceeded: false,
    });
  }

  return projectionData;
}

/**
 * Calculates plus-value data for 30 years or until the limit is exceeded
 * Based on the Excel formulas for Fonciers Réels
 * @param limitExceededYear The year when the revenue limit will be exceeded (if applicable)
 */
function calculatePlusValueData(
  montantInvestissement: number,
  nombreParts: number,
  investementCalculatorData: IInvestementCalculatorData,
  limitExceededYear?: number
): PlusValueDataItem[] {
  const prixAchat = montantInvestissement;
  const plusValueData: PlusValueDataItem[] = [];

  // Fixed acquisition costs - simplified for now
  // In the real implementation, these would come from 'Infos à renseigner'!$L$49 etc.
  const initialAugmentationPrixAcquisition = 15000;
  const laterAugmentationPrixAcquisition = 45000;

  // Calculate the maximum number of years to process
  // If limitExceededYear is provided, we'll stop at that year
  const startYear =
    new Date(investementCalculatorData.dateAcquisition).getFullYear() + 1;
  const { soldByYear } = investementCalculatorData;
  const maxYears = limitExceededYear
    ? Math.min(30, limitExceededYear - startYear + 1)
    : 30;

  for (let i = 0; i < maxYears; i++) {
    // Year calculation (using 1-based index for year number)
    const annee = i + 1;

    // Calculate property value with appreciation
    // Based on formula: =+'Cas d''étude - 1ère année'!$E$6*1.03 for first year
    // Based on formula: =+C53*1.03 for subsequent years
    let prixVente: number;
    if (i === 0) {
      prixVente = Math.round(prixAchat * 1.03);
    } else {
      prixVente = Math.round(plusValueData[i - 1].prixVente * 1.03);
    }

    // Augmentation du prix de vente - simplified for now
    // In the real implementation, this would come from 'Infos à renseigner'!$L$35+'Infos à renseigner'!$L$41
    const augmentationPrixVente = 0;

    // Acquisition costs - simplified for now
    // In the real implementation, this would be more complex based on the Excel formulas
    const augmentationPrixAcquisition =
      i < 5
        ? initialAugmentationPrixAcquisition
        : laterAugmentationPrixAcquisition;

    // Calculate plus-value
    // Based on formula: =+C53+C54-C55-C56
    const plusValue =
      prixVente +
      augmentationPrixVente -
      prixAchat -
      augmentationPrixAcquisition;

    // Calculate plus-value per part (only if positive)
    // Based on formula: =IF(C57>0,+C57/C58,0)
    const plusValueParPart = plusValue > 0 ? plusValue / nombreParts : 0;

    // Income tax abatement calculations
    // Based on the Excel formulas for Taux Abattement Impôt sur le revenu
    // 0% for first 5 years, then 6% per year, capped at 100% after 21 years
    let tauxAbattementImpotRevenu = 0;
    if (i < 5) {
      tauxAbattementImpotRevenu = 0;
    } else if (i === 5) {
      tauxAbattementImpotRevenu = 0.06; // 6% at year 6
    } else if (i < 21) {
      // Add 6% each year
      // Based on formula: =+H61+6%
      tauxAbattementImpotRevenu = 0.06 * (i - 4); // i-4 because we start at 6% for year 6
    } else {
      tauxAbattementImpotRevenu = 1; // 100% after 21 years
    }

    // Calculate abatement
    // Based on formula: =IF(C57<=0,0,+C57*C61)
    const abattementImpotRevenu =
      plusValue <= 0 ? 0 : plusValue * tauxAbattementImpotRevenu;

    // Calculate base taxable
    // Based on formula: =IF(C59>0,+C57-C62,0)
    const baseTaxableImpotRevenu =
      plusValueParPart > 0 ? plusValue - abattementImpotRevenu : 0;

    // Calculate base taxable per part
    // Based on formula: =C63/C58
    const baseTaxableImpotRevenuParPart = baseTaxableImpotRevenu / nombreParts;

    // Calculate income tax (19%)
    // Based on formula: =+C63*0.19
    const impotRevenu = baseTaxableImpotRevenu * 0.19;

    // Social charges abatement calculations
    // Based on the Excel formulas for Taux Abattement Prélèvements Sociaux
    let tauxAbattementPrelevementsSociaux = 0;
    if (i < 5) {
      tauxAbattementPrelevementsSociaux = 0;
    } else if (i === 5) {
      tauxAbattementPrelevementsSociaux = 0.0165; // 1.65% at year 6
    } else if (i < 21) {
      // Add 1.65% each year
      // Based on formula: =+H67+1.65%
      tauxAbattementPrelevementsSociaux = 0.0165 * (i - 4);
    } else if (i === 21) {
      tauxAbattementPrelevementsSociaux = 0.28; // 28% at year 22
    } else if (i === 22) {
      tauxAbattementPrelevementsSociaux = 0.37; // 37% at year 23
    } else if (i === 23) {
      tauxAbattementPrelevementsSociaux = 0.46; // 46% at year 24
    } else if (i === 24) {
      tauxAbattementPrelevementsSociaux = 0.55; // 55% at year 25
    } else if (i === 25) {
      tauxAbattementPrelevementsSociaux = 0.64; // 64% at year 26
    } else if (i === 26) {
      tauxAbattementPrelevementsSociaux = 0.73; // 73% at year 27
    } else if (i === 27) {
      tauxAbattementPrelevementsSociaux = 0.82; // 82% at year 28
    } else if (i === 28) {
      tauxAbattementPrelevementsSociaux = 0.91; // 91% at year 29
    } else {
      tauxAbattementPrelevementsSociaux = 1; // 100% at year 30
    }

    // Calculate abatement
    // Based on formula: =IF(C57<=0,0,+C57*C67)
    const abattementPrelevementsSociaux =
      plusValue <= 0 ? 0 : plusValue * tauxAbattementPrelevementsSociaux;

    // Calculate base taxable
    // Based on formula: =IF(C57<=0,0,+C57-C68)
    const baseTaxablePrelevementsSociaux =
      plusValue <= 0 ? 0 : plusValue - abattementPrelevementsSociaux;

    // Calculate social charges (17.2%)
    // Based on formula: =IF(C69>0,+C69*0.172,0)
    const prelevementsSociauxPlusValue =
      baseTaxablePrelevementsSociaux > 0
        ? baseTaxablePrelevementsSociaux * 0.172
        : 0;

    // Additional taxation - simplified for now
    // In the real implementation, this would use VLOOKUP from 'BAREME TAX° SUP°'
    // Based on formula: =VLOOKUP((C64),'BAREME TAX° SUP°'!$B:$F,4,TRUE)
    const taxationSupplementaire = 0;

    // Total tax on capital gain
    // Based on formula: =+C65+C70+C74
    const totalImpositionPlusValue =
      impotRevenu + prelevementsSociauxPlusValue + taxationSupplementaire;

    // Calculate total cash generated from sale based on the Excel formula: =+C43-'Onglet Emprunt'!$K$24-C65
    // Get the remaining loan amount for this year from soldByYear, or 0 if not available
    const remainingLoanAmount = soldByYear[startYear + annee] || 0;
    // Calculate total cash generated from sale: sale price - tax on capital gain - remaining loan
    const totalTresorerieGeneree =
      prixVente - totalImpositionPlusValue - remainingLoanAmount;

    plusValueData.push({
      annee,
      prixVente,
      augmentationPrixVente,
      prixAchat,
      augmentationPrixAcquisition,
      plusValue,
      nombreParts,
      plusValueParPart,
      tauxAbattementImpotRevenu,
      abattementImpotRevenu,
      baseTaxableImpotRevenu,
      baseTaxableImpotRevenuParPart,
      impotRevenu,
      tauxAbattementPrelevementsSociaux,
      abattementPrelevementsSociaux,
      baseTaxablePrelevementsSociaux,
      prelevementsSociauxPlusValue,
      taxationSupplementaire,
      totalImpositionPlusValue,
      totalTresorerieGeneree,
    });
  }

  return plusValueData;
}

/**
 * Calculates SCI parts data
 * Based on the Excel formulas for Fonciers Réels
 */
function calculateSCIPartsData(
  montantInvestissement: number,
  nombreParts: number,
  projectionData?: ProjectionDataItem[]
): SCIPartsDataItem[] {
  const sciPartsData: SCIPartsDataItem[] = [];

  // Fixed values for simplification
  const valeurOriginePartsSociales = 500; // Based on the Excel formula showing 500 as a fixed value
  const compteCourant = montantInvestissement - valeurOriginePartsSociales; // Based on formula: =+'Cas d''étude - 1ère année'!$K$12-MontantPrêt

  // Loan capital remaining - simplified for now
  // In the real implementation, this would come from 'Onglet Emprunt'
  const loanCapitalRemaining = montantInvestissement * 0.8; // Assuming 80% loan

  // Use the projection data for trésorerie disponible if available
  // Otherwise, use a default array of zeros
  const projectionTresorerie: number[] = projectionData
    ? projectionData.map((item) => item.tresorerieTotaleDegagee)
    : Array(30).fill(0);

  for (let i = 0; i < 30; i++) {
    // Year calculation (using 1-based index for year number)
    const annee = i + 1;

    // Calculate theoretical asset value with 3% annual appreciation
    // Based on formula: =+'Cas d''étude - 1ère année'!$E$6*1.03 for first year
    // Based on formula: =+C82*1.03 for subsequent years
    let valeurTheoriqueActifReel: number;
    if (i === 0) {
      valeurTheoriqueActifReel = Math.round(montantInvestissement * 1.03);
    } else {
      valeurTheoriqueActifReel = Math.round(
        sciPartsData[i - 1].valeurTheoriqueActifReel * 1.03
      );
    }

    // Get trésorerie disponible from projection data
    // Based on formula: =+C45
    const tresorerieDisponible = projectionTresorerie[i];

    // Loan amount decreases over time (simplified model)
    // Based on formula: =+'Onglet Emprunt'!$K$23
    const emprunt =
      i < 25 ? Math.max(0, loanCapitalRemaining * (1 - i / 25)) : 0;

    // Calculate theoretical sale price of shares
    // Based on formula: =+C82+C83-C84-C85
    const prixCessionPartsTheorique =
      valeurTheoriqueActifReel + tresorerieDisponible - emprunt + compteCourant;

    // No additional price increase
    // Based on formula: =+'Infos à renseigner'!$L$35+'Infos à renseigner'!$L$41
    const augmentationPrixVente = 0;

    // Prise en compte gains fiscaux
    // Based on formula: =+C31
    // Use the projection data for resultatFonciersReportables if available
    const priseEnCompteGainsFiscaux =
      projectionData && projectionData[i]
        ? projectionData[i].resultatFonciersReportables
        : 0;

    // Calculate plus-value
    // Based on formula: =+C86+C88-C89+C91
    const plusValue =
      prixCessionPartsTheorique +
      augmentationPrixVente -
      valeurOriginePartsSociales +
      priseEnCompteGainsFiscaux;

    // Calculate plus-value per part
    // Based on formula: =+C92/C93
    const plusValueParPart = plusValue / nombreParts;

    // Income tax abatement calculations
    // Based on the Excel formulas for Taux Abattement Impôt sur le revenu
    // 0% for first 5 years, then 6% per year, capped at 100% after 21 years
    let tauxAbattementImpotRevenu = 0;
    if (i < 5) {
      tauxAbattementImpotRevenu = 0;
    } else if (i === 5) {
      tauxAbattementImpotRevenu = 0.06; // 6% at year 6
    } else if (i < 21) {
      // Add 6% each year
      // Based on formula: =+H96+6%
      tauxAbattementImpotRevenu = 0.06 * (i - 4); // i-4 because we start at 6% for year 6
    } else {
      tauxAbattementImpotRevenu = 1; // 100% after 21 years
    }

    // Calculate abatement
    // Based on formula: =IF(C92<=0,0,+C92*C96)
    const abattementImpotRevenu =
      plusValue <= 0 ? 0 : plusValue * tauxAbattementImpotRevenu;

    // Calculate base taxable
    // Based on formula: =IF(C94>0,+C92-C97,0)
    const baseTaxableImpotRevenu =
      plusValueParPart > 0 ? plusValue - abattementImpotRevenu : 0;

    // Calculate base taxable per part
    // Based on formula: =C63/C58
    const baseTaxableImpotRevenuParPart = baseTaxableImpotRevenu / nombreParts;

    // Calculate income tax (19%)
    // Based on formula: =+C63*0.19
    const impotRevenu = baseTaxableImpotRevenu * 0.19;

    // Social charges abatement calculations
    // Based on the Excel formulas for Taux Abattement Prélèvements Sociaux
    let tauxAbattementPrelevementsSociaux = 0;
    if (i < 5) {
      tauxAbattementPrelevementsSociaux = 0;
    } else if (i === 5) {
      tauxAbattementPrelevementsSociaux = 0.0165; // 1.65% at year 6
    } else if (i < 21) {
      // Add 1.65% each year
      // Based on formula: =+H102+1.65%
      tauxAbattementPrelevementsSociaux = 0.0165 * (i - 4);
    } else if (i === 21) {
      tauxAbattementPrelevementsSociaux = 0.28; // 28% at year 22
    } else if (i === 22) {
      tauxAbattementPrelevementsSociaux = 0.37; // 37% at year 23
    } else if (i === 23) {
      tauxAbattementPrelevementsSociaux = 0.46; // 46% at year 24
    } else if (i === 24) {
      tauxAbattementPrelevementsSociaux = 0.55; // 55% at year 25
    } else if (i === 25) {
      tauxAbattementPrelevementsSociaux = 0.64; // 64% at year 26
    } else if (i === 26) {
      tauxAbattementPrelevementsSociaux = 0.73; // 73% at year 27
    } else if (i === 27) {
      tauxAbattementPrelevementsSociaux = 0.82; // 82% at year 28
    } else if (i === 28) {
      tauxAbattementPrelevementsSociaux = 0.91; // 91% at year 29
    } else {
      tauxAbattementPrelevementsSociaux = 1; // 100% at year 30
    }

    // Calculate abatement
    // Based on formula: =IF(C92<=0,0,+C92*C102)
    const abattementPrelevementsSociaux =
      plusValue <= 0 ? 0 : plusValue * tauxAbattementPrelevementsSociaux;

    // Calculate base taxable
    // Based on formula: =IF(C92<=0,0,+C92-C103)
    const baseTaxablePrelevementsSociaux =
      plusValue <= 0 ? 0 : plusValue - abattementPrelevementsSociaux;

    // Calculate social charges (17.2%)
    // Based on formula: =IF(C104>0,+C104*0.172,0)
    const prelevementsSociauxPlusValue =
      baseTaxablePrelevementsSociaux > 0
        ? baseTaxablePrelevementsSociaux * 0.172
        : 0;

    // Additional taxation - simplified for now
    // In the real implementation, this would use VLOOKUP from 'BAREME TAX° SUP°'
    // Based on formula: =VLOOKUP((C99),'BAREME TAX° SUP°'!$B:$F,4,TRUE)
    const taxationSupplementaire = 0;

    // Total tax on capital gain
    // Based on formula: =+C100+C105+C109
    const totalImpositionPlusValue =
      impotRevenu + prelevementsSociauxPlusValue + taxationSupplementaire;

    sciPartsData.push({
      annee,
      valeurTheoriqueActifReel,
      tresorerieDisponible,
      emprunt,
      compteCourant,
      prixCessionPartsTheorique,
      augmentationPrixVente,
      valeurOriginePartsSociales,
      priseEnCompteGainsFiscaux,
      plusValue,
      nombreParts,
      plusValueParPart,
      tauxAbattementImpotRevenu,
      abattementImpotRevenu,
      baseTaxableImpotRevenu,
      baseTaxableImpotRevenuParPart,
      impotRevenu,
      tauxAbattementPrelevementsSociaux,
      abattementPrelevementsSociaux,
      baseTaxablePrelevementsSociaux,
      prelevementsSociauxPlusValue,
      taxationSupplementaire,
      totalImpositionPlusValue,
    });
  }

  return sciPartsData;
}
